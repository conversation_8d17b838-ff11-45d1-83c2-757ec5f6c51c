package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.rule.*;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.rule.RuleAddParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.HutoolUtil;
import com.cowell.scib.utils.JacksonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:23
 */
@Slf4j
@Service
public class RuleServiceImpl extends RulePriService implements RuleService {

    @Autowired
    private CommonDictionaryExtendMapper commonDictionaryExtendMapper;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;

    @Autowired
    private ConfigOrgDetailMapper configOrgDetailMapper;

    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;

    @Autowired
    private ConfigOrgDetailExtendExtendMapper configOrgDetailExtendExtendMapper;

    @Autowired
    private ConfigOrgDetailExtendMapper configOrgDetailExtendMapper;

    @Autowired
    private OnlineHotGoodsExtendMapper onlineHotGoodsExtendMapper;

    @Autowired
    private OnlineHotGoodsMapper onlineHotGoodsMapper;

    @Autowired
    private ConfigOrgMapper configOrgMapper;

    @Autowired
    private ConfigOrgUnmanageCategoryMapper configOrgUnmanageCategoryMapper;

    @Autowired
    private RuleAddFactory ruleAddFactory;

    @Autowired
    private SearchService searchService;
    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Autowired
    private CheckService checkService;
    @Value("${scib.task.bdp.environment:300}")
    private int environment;
    @Autowired
    private SendService sendService;

    @Override
    public Map<String, RuleDetailDTO> getRuleList(RuleParam ruleParam, TokenUserDTO userDTO) {
        log.debug("getRuleList|ruleParam:{}.", JSONObject.toJSONString(ruleParam));
        int permissionCode = RulePermEnum.P_UNEDIT.getCode();//默认不可编辑
        if (null != userDTO) {
            if (null != ruleParam.getResourceId()) {
                permissionCode = checkService.operatePermWhitResource(ruleParam.getOrgId(), ruleParam.getScopeCode(), ruleParam.getResourceId(), userDTO.getUserId());
            } else {
                permissionCode = checkService.operatePerm(ruleParam.getOrgId(), ruleParam.getScopeCode(), userDTO.getUserId());
            }
        }
        log.debug("getRuleList|permissionCode:{}.", permissionCode);
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(ruleParam.getScopeCode());
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleParam.getOrgId(),Byte.valueOf(ruleParam.getConfigType()), null);
        ArrayList<RuleDetailDTO> nullRuleDetailDTO = new ArrayList<>();
        if (Objects.isNull(configOrg)){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, permissionCode);
        }
        //只查询当前域得数据
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(),commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, permissionCode);
        }

        List<ConfigOrgDetailExtend> configOrgDetailExtends = configOrgDetailExtendExtendMapper.selectByDetailId(configOrg.getId(), null);
        Map<Long, List<ConfigOrgDetailExtend>> configDetailExtendMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(configOrgDetailExtends)) {
            configDetailExtendMap = configOrgDetailExtends.stream().collect(Collectors.groupingBy(ConfigOrgDetailExtend::getDetailId));
        }
        Map<Long, List<ConfigOrgDetailExtend>> finalConfigDetailExtendMap = configDetailExtendMap;

        return getRuleDetailDTOMap(commonDictionaries,configOrgDetails,permissionCode,finalConfigDetailExtendMap);
    }

    @Override
    public Map<String, RuleDetailDTO> getConfigRuleList(Long orgId, List<String> scopeCodeList, Byte configType){
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectListByScopeCode(scopeCodeList);
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(orgId, configType, null);
        ArrayList<RuleDetailDTO> nullRuleDetailDTO = new ArrayList<>();
        if (Objects.isNull(configOrg)){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, RulePermEnum.P_UNEDIT.getCode());
        }
        //只查询当前域得数据
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(),commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, RulePermEnum.P_UNEDIT.getCode());
        }

        List<ConfigOrgDetailExtend> configOrgDetailExtends = configOrgDetailExtendExtendMapper.selectByDetailId(configOrg.getId(), null);
        Map<Long, List<ConfigOrgDetailExtend>> configDetailExtendMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(configOrgDetailExtends)) {
            configDetailExtendMap = configOrgDetailExtends.stream().collect(Collectors.groupingBy(ConfigOrgDetailExtend::getDetailId));
        }
        Map<Long, List<ConfigOrgDetailExtend>> finalConfigDetailExtendMap = configDetailExtendMap;

        return getRuleDetailDTOMap(commonDictionaries,configOrgDetails,RulePermEnum.P_UNEDIT.getCode(), finalConfigDetailExtendMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initConfigRuleInfo(Long orgId, TokenUserDTO userDTO, Integer version){
        try {
            for (DicApiEnum dicEnableEnum : DicApiEnum.values()) {
                log.debug("initConfigRuleInfo|开始初始化|{}.", dicEnableEnum.getCode());

                boolean checkResult = checkService.judgeEditAble(orgId, dicEnableEnum.getCode(), userDTO.getUserId());
                log.info("initConfigRuleInfo||checkResult:{}.", checkResult);
                if(!checkResult){
                    log.warn("initConfigRuleInfo|权限不足，初始化失败|orgId:{}.|userId:{}.|scopeCode:{}.", orgId, userDTO.getUserId(), dicEnableEnum.getCode());
                    continue;
                }

                /**
                 * 获取默认值不为空的字典
                 */
                List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectDefValueByScopeCode(dicEnableEnum.getCode());
                if (CollectionUtils.isEmpty(commonDictionaries)){
                    log.warn("initConfigRuleInfo|域编码不存在，初始化失败|orgId:{}.|userId:{}.|scopeCode:{}.", orgId, userDTO.getUserId(), dicEnableEnum.getCode());
                    continue;
                }

                //添加配置
                long configId = addConfig(orgId, dicEnableEnum.getConfigType(), userDTO, version);
                log.info("initConfigRuleInfo|插入config主表成功：{}",configId);
//                //删除详情数据（先删后增原则）
//                configOrgDetailExMapper.deleteByconfigIdAndDictCodes(configId, commonDictionaries.stream().filter(v -> !v.getDictCode().equals(Constants.GOODS_BLACK_LIST)).map(CommonDictionary::getDictCode).collect(Collectors.toList()));
//                if (commonDictionaries.contains(Constants.STORE_BLACK_LIST) || commonDictionaries.contains(Constants.STORE_WHITE_LIST)) {
//                    configOrgDetailExtendExtendMapper.deleteByconfigIdAndType(configId, Constants.STORE);
//                }

                //插入详情
                List<ConfigOrgDetail> configOrgDetailList = Lists.newArrayList();
                for(CommonDictionary dicValueDetail : commonDictionaries){
                    if(PerprotyTypeEnum.isObj(dicEnableEnum.getPerprotyType())){
                        ConfigOrgDetail configOrgDetail1 = new ConfigOrgDetail();
                        configOrgDetail1.setConfigId(configId);
                        configOrgDetail1.setPerprotyType(dicEnableEnum.getPerprotyType());
                        configOrgDetail1.setDictCode(dicValueDetail.getDictCode());
                        configOrgDetail1.setPerprotyValue(dicValueDetail.getDefaultValue());
                        buildConfigOrgDetailCommonInfo(configOrgDetail1,userDTO, version);
                        configOrgDetailList.add(configOrgDetail1);
                    }else if(PerprotyTypeEnum.isCheckbox(dicEnableEnum.getPerprotyType())){
                        String[] checkBoxValues = dicValueDetail.getDefaultValue().split(Constants.VALUE_SPLITE);
                        for(String checkBoxValue : checkBoxValues){
                            ConfigOrgDetail configOrgDetail1 = new ConfigOrgDetail();
                            configOrgDetail1.setConfigId(configId);
                            configOrgDetail1.setPerprotyType(dicEnableEnum.getPerprotyType());
                            configOrgDetail1.setDictCode(dicValueDetail.getDictCode());
                            configOrgDetail1.setPerprotyValue(checkBoxValue);
                            buildConfigOrgDetailCommonInfo(configOrgDetail1,userDTO,version);
                            configOrgDetailList.add(configOrgDetail1);
                        }
                    }
                }
                //入库
                if(CollectionUtils.isNotEmpty(configOrgDetailList)){
                    configOrgDetailExMapper.insertConfigOrgDetailList(configOrgDetailList);
                }
            }
        } catch (Exception e) {
            log.warn("initConfigRuleInfo|初始化异常。", e);
            throw e;
        }
    }

    @Override
    public void add(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        log.info("数字化商品规则添加参数：{}",JSONObject.toJSONString(ruleAddParam));
        boolean checkResult = false;
        if (null != ruleAddParam.getResourceId()) {
            checkResult = checkService.judgeEditAbleWithResource(ruleAddParam.getOrgId(), ruleAddParam.getScopeCode(), userDTO.getUserId(), ruleAddParam.getResourceId());
        } else {
            checkResult = checkService.judgeEditAble(ruleAddParam.getOrgId(), ruleAddParam.getScopeCode(), userDTO.getUserId());
        }
        log.info("add||checkResult:{}.", checkResult);
        if(!checkResult){
            throw new BusinessErrorException("该用户无此操作权限");
        }
        //校验
        IRuleAddService iRuleAddService = ruleAddFactory.getPolicy(ruleAddParam.getScopeCode());
        if(Objects.nonNull(iRuleAddService)){
            iRuleAddService.check(ruleAddParam,userDTO);
        }

        //添加配置
        long configId = addConfig(ruleAddParam.getOrgId(), Byte.parseByte(ruleAddParam.getConfigType()), userDTO, null);

        //删除数据
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(ruleAddParam.getScopeCode());
        configOrgDetailExMapper.deleteByconfigIdAndDictCodes(configId, commonDictionaries.stream().filter(v -> !v.getDictCode().equals(ruleAddParam.getGoodsDictCode())).map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (commonDictionaries.contains(Constants.STORE_BLACK_LIST) || commonDictionaries.contains(Constants.STORE_WHITE_LIST)
        || commonDictionaries.contains(Constants.JYMU_STORE_BLACK_LIST) || commonDictionaries.contains(Constants.JYMU_STORE_WHITE_LIST)
        || commonDictionaries.contains(Constants.BLKC_4_1_BUSINESSLIST) || commonDictionaries.contains(Constants.BLKC_4_1_STOREWHITELIST)
        || commonDictionaries.contains(Constants.BLKC_4_1_STOREBLACKLIST) || commonDictionaries.contains(Constants.BLKC_4_2_BUSINESSLIST)
        || commonDictionaries.contains(Constants.BLKC_4_2_STOREWHITELIST) || commonDictionaries.contains(Constants.BLKC_4_2_STOREBLACKLIST)
        ) {
            configOrgDetailExtendExtendMapper.deleteByconfigIdAndType(configId, Constants.STORE);
        }

        Map<String,List<ConfigOrgDetailExtend>> configOrgDetailExtendMap=new HashMap<>();
        List<ConfigOrgDetail> configOrgDetails = buildConfigOrgDetail(ruleAddParam, userDTO, configId, configOrgDetailExtendMap);
        if (CollectionUtils.isNotEmpty(configOrgDetails)){
            configOrgDetailExMapper.insertConfigOrgDetailList(configOrgDetails);
        }
        if (MapUtils.isEmpty(configOrgDetailExtendMap)){
            return;
        }
        List<ConfigOrgDetail> configOrgDetailsSave = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configId,commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configOrgDetailsSave)){
            return;
        }
        Map<String, ConfigOrgDetail> tempConfigOrgDetailMap = configOrgDetailsSave.stream().filter(v -> v.getPerprotyType() == Constants.COLLECTION).collect(Collectors.toMap(ConfigOrgDetail::getDictCode, Function.identity(), (k1, k2) -> k1));
        if (MapUtils.isEmpty(tempConfigOrgDetailMap)){
            return;
        }

        List<ConfigOrgDetailExtend> configOrgDetailExtendList = new ArrayList<>();
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.STORE_WHITE_LIST))){
            List<ConfigOrgDetailExtend> storeWhiteListExtends = configOrgDetailExtendMap.get(Constants.STORE_WHITE_LIST);
            log.info("白名单storeWhiteListExtends：{}",JSONObject.toJSONString(storeWhiteListExtends));
            if (CollectionUtils.isNotEmpty(storeWhiteListExtends)){
                storeWhiteListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.STORE_WHITE_LIST).getId());});
                configOrgDetailExtendList.addAll(storeWhiteListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.STORE_BLACK_LIST))){
            List<ConfigOrgDetailExtend> storeBlackListExtends = configOrgDetailExtendMap.get(Constants.STORE_BLACK_LIST);
            log.info("黑名单storeBlackListExtends：{}",JSONObject.toJSONString(storeBlackListExtends));
            if (CollectionUtils.isNotEmpty(storeBlackListExtends)){
                storeBlackListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.STORE_BLACK_LIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.JYMU_STORE_BLACK_LIST))){
            List<ConfigOrgDetailExtend> storeBlackListExtends = configOrgDetailExtendMap.get(Constants.JYMU_STORE_BLACK_LIST);
            log.info("经营目录门店黑名单storeBlackListExtends：{}",JSONObject.toJSONString(storeBlackListExtends));
            if (CollectionUtils.isNotEmpty(storeBlackListExtends)){
                storeBlackListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.JYMU_STORE_BLACK_LIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.JYMU_STORE_WHITE_LIST))){
            List<ConfigOrgDetailExtend> storeWhiteListExtends = configOrgDetailExtendMap.get(Constants.JYMU_STORE_WHITE_LIST);
            log.info("经营目录参与选配门店storeWhiteListExtends：{}",JSONObject.toJSONString(storeWhiteListExtends));
            if (CollectionUtils.isNotEmpty(storeWhiteListExtends)){
                storeWhiteListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.JYMU_STORE_WHITE_LIST).getId());});
                configOrgDetailExtendList.addAll(storeWhiteListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_BUSINESSLIST))){
            List<ConfigOrgDetailExtend> businessListExtends = configOrgDetailExtendMap.get(Constants.BLKC_4_1_BUSINESSLIST);
            log.info("效期品参与企业范围：{}",JSONObject.toJSONString(businessListExtends));
            if (CollectionUtils.isNotEmpty(businessListExtends)){
                businessListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_BUSINESSLIST).getId());});
                configOrgDetailExtendList.addAll(businessListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_STOREWHITELIST))){
            List<ConfigOrgDetailExtend> storeWhiteList = configOrgDetailExtendMap.get(Constants.BLKC_4_1_STOREWHITELIST);
            log.info("效期品门店白名单：{}",JSONObject.toJSONString(storeWhiteList));
            if (CollectionUtils.isNotEmpty(storeWhiteList)){
                storeWhiteList.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_STOREWHITELIST).getId());});
                configOrgDetailExtendList.addAll(storeWhiteList);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_STOREBLACKLIST))){
            List<ConfigOrgDetailExtend> storeBlackList = configOrgDetailExtendMap.get(Constants.BLKC_4_1_STOREBLACKLIST);
            log.info("效期品门店黑名单：{}",JSONObject.toJSONString(storeBlackList));
            if (CollectionUtils.isNotEmpty(storeBlackList)){
                storeBlackList.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_1_STOREBLACKLIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackList);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_BUSINESSLIST))){
            List<ConfigOrgDetailExtend> businessListExtends = configOrgDetailExtendMap.get(Constants.BLKC_4_2_BUSINESSLIST);
            log.info("定淘品参与企业范围：{}",JSONObject.toJSONString(businessListExtends));
            if (CollectionUtils.isNotEmpty(businessListExtends)){
                businessListExtends.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_BUSINESSLIST).getId());});
                configOrgDetailExtendList.addAll(businessListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_STOREWHITELIST))){
            List<ConfigOrgDetailExtend> storeWhiteList = configOrgDetailExtendMap.get(Constants.BLKC_4_2_STOREWHITELIST);
            log.info("定淘品门店白名单：{}",JSONObject.toJSONString(storeWhiteList));
            if (CollectionUtils.isNotEmpty(storeWhiteList)){
                storeWhiteList.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_STOREWHITELIST).getId());});
                configOrgDetailExtendList.addAll(storeWhiteList);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_STOREBLACKLIST))){
            List<ConfigOrgDetailExtend> storeBlackList = configOrgDetailExtendMap.get(Constants.BLKC_4_2_STOREBLACKLIST);
            log.info("定淘品门店黑名单：{}",JSONObject.toJSONString(storeBlackList));
            if (CollectionUtils.isNotEmpty(storeBlackList)){
                storeBlackList.stream().forEach(v->{v.setDetailId(tempConfigOrgDetailMap.get(Constants.BLKC_4_2_STOREBLACKLIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackList);
            }
        }

        if(CollectionUtils.isNotEmpty(configOrgDetailExtendList)){
            configOrgDetailExtendExtendMapper.insertConfigOrgDetailExtendList(configOrgDetailExtendList);
        }
    }

    @Override
    public Map<String, List<OptionDto>> getRuleEnumList(RuleParam ruleParam, TokenUserDTO userDTO) {
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(ruleParam.getScopeCode());
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }

        List<String> dicCodeList = commonDictionaries.stream().filter(v-> StringUtils.isNotBlank(v.getDictCode())).map(v->v.getDictCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dicCodeList)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }

        List<CommonEnums> commonEnumsList = commonEnumsExtendMapper.selectByDicCodeList(dicCodeList);
        if (CollectionUtils.isEmpty(commonEnumsList)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_ENUM_NOT_EXIST.getMsg());
        }
        Map<String, List<CommonEnums>> commonEnumsMap = commonEnumsList.stream().filter(v->StringUtils.isNotBlank(v.getEnumValue())).collect(Collectors.groupingBy(CommonEnums::getPropertyCode));
        Map<String, List<OptionDto>> optionMap = new HashMap<>(commonEnumsMap.size());
        commonEnumsMap.forEach((k,list)->{
            List<OptionDto> optionDtoList = list.stream().map(v->{
                return new OptionDto(v.getEnumName(), v.getEnumValue());
            }).collect(Collectors.toList());
            optionMap.put(k, optionDtoList);
        });

        return optionMap;
    }

    @Override
    public void updateRule(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleAddParam.getOrgId(), Byte.valueOf(ruleAddParam.getConfigType()), null);
        if (Objects.isNull(configOrg)){
            return;
        }
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(ruleAddParam.getScopeCode());
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }
        configOrgDetailExMapper.deleteByconfigIdAndDictCodes(configOrg.getId(),commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (commonDictionaries.contains(Constants.STORE_BLACK_LIST) || commonDictionaries.contains(Constants.STORE_WHITE_LIST)) {
            configOrgDetailExtendExtendMapper.deleteByconfigIdAndType(configOrg.getId(), Constants.STORE);
        }
        add(ruleAddParam,userDTO);
    }

    @Override
    public List<ErrorGoodsDTO> importGoods(MultipartFile file, Long orgId, String scopeCode, String configType, String dictCode, TokenUserDTO userDTO) throws Exception {
        try {
            List<CommonGoodsDTO> goodsInfos = resolveAndCheck(file, orgId, scopeCode, configType);
            //添加配置
            long configId = addConfig(orgId, Byte.parseByte(configType), userDTO, null);
            long detailId=0;
            List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configId,Arrays.asList(dictCode));
            if (CollectionUtils.isEmpty(configOrgDetails)){
                detailId = inserExcludeAttribute(userDTO, configId, dictCode);
            }else {
                detailId= configOrgDetails.get(0).getId().intValue();
            }

            return addGoodsExtend(configId, detailId, goodsInfos,userDTO);
        } catch (Exception e) {
            throw e;
        }
    }

    private List<ErrorGoodsDTO> addGoodsExtend(Long configId, Long detailId, List<CommonGoodsDTO> goodsInfos, TokenUserDTO userDTO) {
        CopyOnWriteArrayList<CommonGoodsDTO> goodsDTOS = new CopyOnWriteArrayList<>();
        CopyOnWriteArrayList<ErrorGoodsDTO> errorList = new CopyOnWriteArrayList<>();
        Lists.partition(goodsInfos, Constants.QUERY_SEARCH_PAGESIZE).parallelStream().forEach(v -> {
            Map<String, SpuListVo> spuVOMap = searchService.getSpuVOMap(v.stream().map(CommonGoodsDTO::getGoodsNo).collect(Collectors.toList()));
//            Map<String, SpuListVo> spuVOMap = new HashMap<>();
//            spuVOMap.put("1010611", new SpuListVo());
            v.forEach(goo -> {
                SpuListVo spuVo = spuVOMap.get(goo.getGoodsNo());
                if (Objects.nonNull(spuVo)) {
                    CommonGoodsDTO goodsDTO = getGoodsDTO(spuVo);
                    goodsDTOS.add(goodsDTO);
                } else {
                    ErrorGoodsDTO impErrDTO = new ErrorGoodsDTO();
                    impErrDTO.setGoodsNo(goo.getGoodsNo());
                    impErrDTO.setErrorMsg("商品编码不存在");
                    errorList.add(impErrDTO);
                }
            });
        });
        // insert
        if (CollectionUtils.isNotEmpty(goodsDTOS)) {
            // 先删在增
            ConfigOrgDetailExtendExample example = new ConfigOrgDetailExtendExample();
            example.createCriteria().andConfigIdEqualTo(configId).andDetailIdEqualTo(detailId).andExtendTypeEqualTo(ConfigOrgExtendTypeEnum.GOODS.getCode());
            configOrgDetailExtendMapper.deleteByExample(example);
            Lists.partition(goodsDTOS, Constants.INSERT_MAX_SIZE).forEach(v -> {
                configOrgDetailExtendExtendMapper.batchInsert(v.stream().map(g -> {
                    ConfigOrgDetailExtend extend = new ConfigOrgDetailExtend();
                    extend.setConfigId(configId);
                    extend.setDetailId(detailId);
                    extend.setExtendType(ConfigOrgExtendTypeEnum.GOODS.getCode());
                    extend.setKeyword(g.getGoodsNo());
                    extend.setExtend(JSON.toJSONString(g));
                    extend.setCreatedBy(userDTO.getUserId());
                    extend.setCreatedName(userDTO.getName());
                    extend.setUpdatedBy(userDTO.getUserId());
                    extend.setUpdatedName(userDTO.getName());
                    return extend;
                }).collect(Collectors.toList()));
            });
        }
        return errorList;
    }

    private CommonGoodsDTO getGoodsDTO(SpuListVo spuVo) {
        CommonGoodsDTO goodsDTO = new CommonGoodsDTO();
        goodsDTO.setGoodsNo(spuVo.getGoodsNo());
        goodsDTO.setDescription(spuVo.getDescription());
        goodsDTO.setGoodsCommonName(spuVo.getCurName());
        goodsDTO.setManufacturer(spuVo.getFactoryid());
        goodsDTO.setDosageForm(spuVo.getDosageformsid());
        goodsDTO.setGoodsUnit(spuVo.getGoodsunit());
        goodsDTO.setSpecifications(spuVo.getJhiSpecification());
        return goodsDTO;
    }

    private Map<String, RuleDetailDTO> getStringRuleDetailDTOMap(List<CommonDictionary> commonDictionaries, ArrayList<RuleDetailDTO> nullRuleDetailDTO,int permissionCode) {
        commonDictionaries.stream().forEach(v->{
            RuleDetailDTO ruleDetailDTO = new RuleDetailDTO();
            BeanUtils.copyProperties(v,ruleDetailDTO);
            buildDefaultValue(ruleDetailDTO, v.getDefaultValue());
            if (RulePermEnum.P_SUPPER.getCode()==permissionCode){
                ruleDetailDTO.setDisAble(DicEnableEnum.UNDISABLE.isValue());
            } else if (RulePermEnum.P_EDIT.getCode()==permissionCode){
                ruleDetailDTO.setDisAble(DicEnableEnum.getResultByCode(v.getEditAble()));
            }else {
                ruleDetailDTO.setDisAble(DicEnableEnum.DISABLE.isValue());
            }
            nullRuleDetailDTO.add(ruleDetailDTO);
        });
        return nullRuleDetailDTO.stream().collect(Collectors.toMap(RuleDetailDTO::getDictCode, Function.identity(), (k1, k2) -> k1));
    }

    private Map<String, RuleDetailDTO> getRuleDetailDTOMap(List<CommonDictionary> commonDictionaries,  List<ConfigOrgDetail> configOrgDetails,int permissionCode,Map<Long, List<ConfigOrgDetailExtend>> finalConfigDetailExtendMap ) {
        Map<String, List<ConfigOrgDetail>> ruleDetailMap=new HashMap<>();
        ArrayList<RuleDetailDTO> ruleDetailDTOSList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(configOrgDetails)){
            ruleDetailMap = configOrgDetails.stream().collect(Collectors.groupingBy(ConfigOrgDetail::getDictCode));
        }

        Map<String, List<ConfigOrgDetail>> finalRuleDetailMap = ruleDetailMap;
        commonDictionaries.stream().forEach(v->{
            RuleDetailDTO ruleDetailDTO = new RuleDetailDTO();
            if (MapUtils.isEmpty(finalRuleDetailMap) || CollectionUtils.isEmpty(finalRuleDetailMap.get(v.getDictCode()))){
                BeanUtils.copyProperties(v,ruleDetailDTO);
                buildDefaultValue(ruleDetailDTO, v.getDefaultValue());
            }else {
                List<ConfigOrgDetail> ruleDetailDTOS = finalRuleDetailMap.get(v.getDictCode());
                if (CollectionUtils.isNotEmpty(ruleDetailDTOS) && ruleDetailDTOS.size() == 1) {
                    BeanUtils.copyProperties(ruleDetailDTOS.get(0), ruleDetailDTO);
                    ruleDetailDTO.setDetailId(ruleDetailDTOS.get(0).getId());
                    if (ruleDetailDTO.getPerprotyType().equals(Constants.CHECKBOX)){
                        ruleDetailDTO.setPerprotyValueList(Arrays.asList(ruleDetailDTO.getPerprotyValue()));
                        ruleDetailDTO.setPerprotyValue(null);
                    }
                } else if (CollectionUtils.isNotEmpty(ruleDetailDTOS) && ruleDetailDTOS.size() > 1) {
                    List<String> collect = ruleDetailDTOS.stream().filter(c -> c.getPerprotyType().equals(Constants.CHECKBOX)).map(c -> c.getPerprotyValue()).collect(Collectors.toList());
                    BeanUtils.copyProperties(ruleDetailDTOS.get(0), ruleDetailDTO);
                    ruleDetailDTO.setDetailId(ruleDetailDTOS.get(0).getId());
                    ruleDetailDTO.setPerprotyValue(null);
                    ruleDetailDTO.setPerprotyValueList(collect);
                }
                if (ruleDetailDTO.getPerprotyType().equals(Constants.COLLECTION) && MapUtils.isNotEmpty(finalConfigDetailExtendMap)) {
                    if (CollectionUtils.isNotEmpty(finalConfigDetailExtendMap.get(ruleDetailDTO.getDetailId()))) {
                        ruleDetailDTO.setOrgIdList(finalConfigDetailExtendMap.get(ruleDetailDTO.getDetailId()).stream().filter(c -> c.getExtendType().equals(Constants.STORE)).map(c -> Long.valueOf(c.getKeyword())).collect(Collectors.toList()));
                        ruleDetailDTO.setGoodsCodeList(finalConfigDetailExtendMap.get(ruleDetailDTO.getDetailId()).stream().filter(c -> c.getExtendType().equals(Constants.GOODS)).collect(Collectors.toList()));
                    }
                }
            }
            if (RulePermEnum.P_SUPPER.getCode()==permissionCode){
                ruleDetailDTO.setDisAble(DicEnableEnum.UNDISABLE.isValue());
            } else if (RulePermEnum.P_EDIT.getCode()==permissionCode){
                ruleDetailDTO.setDisAble(DicEnableEnum.getResultByCode(v.getEditAble()));
            }else {
                ruleDetailDTO.setDisAble(DicEnableEnum.DISABLE.isValue());
            }
            ruleDetailDTO.setDictName(v.getDictName());
            ruleDetailDTOSList.add(ruleDetailDTO);
        });
        return ruleDetailDTOSList.stream().collect(Collectors.toMap(RuleDetailDTO::getDictCode, Function.identity(), (k1, k2) -> k1));
    }



        private List<ConfigOrgDetail> buildConfigOrgDetail(RuleAddParam ruleAddParam,TokenUserDTO userDTO,long configId,Map<String,List<ConfigOrgDetailExtend>> configOrgDetailExtendMap){
        List<ConfigOrgDetail> configOrgDetailList = new ArrayList<>();
        if (MapUtils.isEmpty(ruleAddParam.getDetailMap())){
            return configOrgDetailList;
        }
        Set<Map.Entry<String, RuleDetailDTO>> entries = ruleAddParam.getDetailMap().entrySet();
        Iterator<Map.Entry<String, RuleDetailDTO>> iterator = entries.iterator();
        while (iterator.hasNext()){
            ConfigOrgDetail configOrgDetail = new ConfigOrgDetail();
            configOrgDetail.setConfigId(Long.valueOf(configId));
            Map.Entry<String, RuleDetailDTO> next = iterator.next();
            configOrgDetail.setDictCode(next.getKey());
            RuleDetailDTO ruleDetailDTO = next.getValue();
            if (Objects.isNull(ruleDetailDTO)){
                configOrgDetailList.add(configOrgDetail);
            }else {
                if (CollectionUtils.isNotEmpty(ruleDetailDTO.getPerprotyValueList())){
                    for (String s : ruleDetailDTO.getPerprotyValueList()) {
                        ConfigOrgDetail configOrgDetail1 = new ConfigOrgDetail();
                        BeanUtils.copyProperties(configOrgDetail,configOrgDetail1);
                        configOrgDetail1.setPerprotyValue(s);
                        configOrgDetail1.setPerprotyType(Constants.CHECKBOX);
                        buildConfigOrgDetailCommonInfo(configOrgDetail1,userDTO, null);
                        configOrgDetailList.add(configOrgDetail1);
                    }
                }
                if (StringUtils.isNotEmpty(ruleDetailDTO.getPerprotyValue())){
                    configOrgDetail.setPerprotyValue(ruleDetailDTO.getPerprotyValue());
                    configOrgDetail.setPerprotyType(Constants.OBJECT);
                    buildConfigOrgDetailCommonInfo(configOrgDetail,userDTO, null);
                    configOrgDetailList.add(configOrgDetail);
                }
                if (CollectionUtils.isNotEmpty(ruleDetailDTO.getOrgIdList())){
                    ArrayList<ConfigOrgDetailExtend> configOrgDetailExtendList = new ArrayList<>();
                    configOrgDetail.setPerprotyValue("");
                    configOrgDetail.setPerprotyType(Constants.COLLECTION);
                    for (Long aLong : ruleDetailDTO.getOrgIdList()) {
                        ConfigOrgDetailExtend configOrgDetailExtend = new ConfigOrgDetailExtend();
                        configOrgDetailExtend.setConfigId(Long.valueOf(configId));
                        configOrgDetailExtend.setExtendType(Constants.STORE);
                        configOrgDetailExtend.setKeyword(String.valueOf(aLong));
                        buildConfigOrgDetailExtendCommonInfo(configOrgDetailExtend,userDTO);
                        configOrgDetailExtendList.add(configOrgDetailExtend);
                    }
                    configOrgDetailExtendMap.put(next.getKey(),configOrgDetailExtendList);
                    buildConfigOrgDetailCommonInfo(configOrgDetail,userDTO, null);
                    configOrgDetailList.add(configOrgDetail);
                }
            }
        }
        return configOrgDetailList;
    }


    private void buildConfigOrgDetailCommonInfo(ConfigOrgDetail configOrgDetail, TokenUserDTO userDTO, Integer version) {
        configOrgDetail.setStatus(Constants.NORMAL_STATUS);
        configOrgDetail.setExtend("");
        configOrgDetail.setVersion(version==null?Constants.DEF_VERSION:version);
        configOrgDetail.setCreatedBy(userDTO.getUserId());
        configOrgDetail.setCreatedName(userDTO.getName());
        configOrgDetail.setUpdatedBy(userDTO.getUserId());
        configOrgDetail.setUpdatedName(userDTO.getName());
    }

    private void buildConfigOrgDetailExtendCommonInfo(ConfigOrgDetailExtend configOrgDetailExtend, TokenUserDTO userDTO) {
        configOrgDetailExtend.setStatus(Constants.NORMAL_STATUS);
        if (Objects.isNull(configOrgDetailExtend.getExtend())){
            configOrgDetailExtend.setExtend("");
        }
        configOrgDetailExtend.setVersion(0);
        configOrgDetailExtend.setCreatedBy(userDTO.getUserId());
        configOrgDetailExtend.setCreatedName(userDTO.getName());
        configOrgDetailExtend.setUpdatedBy(userDTO.getUserId());
        configOrgDetailExtend.setUpdatedName(userDTO.getName());
    }


    @Override
    public void addGroupBlackGoods(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleAddParam.getOrgId(), Byte.parseByte(ruleAddParam.getConfigType()), null);
        Long configId=0L;
        Long detailId=0L;
        if (Objects.isNull(configOrg)){
            long i = addConfig(ruleAddParam.getOrgId(), Byte.parseByte(ruleAddParam.getConfigType()), userDTO, null);
            configId=Long.valueOf(i);
            detailId=inserExcludeAttribute(userDTO,configId,ruleAddParam.getGoodsDictCode());
        }else {
            List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(),Arrays.asList(ruleAddParam.getGoodsDictCode()));
            configId=configOrg.getId();
            if (CollectionUtils.isEmpty(configOrgDetails)){
                detailId = inserExcludeAttribute(userDTO, configOrg.getId(), ruleAddParam.getGoodsDictCode());
            }else {
                detailId= configOrgDetails.get(0).getId();
            }
        }
        List<ConfigOrgDetailExtend> configOrgDetailExtends1 = configOrgDetailExtendExtendMapper.selectByDetailId(configId, Long.valueOf(detailId));
        List<String> dbGoodsList = configOrgDetailExtends1.stream().map(ConfigOrgDetailExtend::getKeyword).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dbGoodsList)){
            List<String> collect = ruleAddParam.getGoodsNoList().stream().distinct().collect(Collectors.toList());
            insertConfigOrgDetailExtend(collect, userDTO, configId,detailId);
        }else {
            List<String> collect = ruleAddParam.getGoodsNoList().stream().distinct().collect(Collectors.toList());
            List<String> commonGoodList = collect.stream().filter(dbGoodsList::contains).collect(Collectors.toList());
            collect.removeAll(commonGoodList);
            insertConfigOrgDetailExtend(collect, userDTO, configId,detailId);
        }
    }

    private void insertConfigOrgDetailExtend(List<String> collect, TokenUserDTO userDTO, Long configId,Long detailId) {

        Map<String, SpuListVo> spuVOMap=new HashMap<>();
        List<List<String>> partition = Lists.partition(collect, Constants.QUERY_SEARCH_PAGESIZE);
        for (List<String> strings : partition) {
            Map<String, SpuListVo> spuVOMap1 = searchService.getSpuVOMap(strings);
            spuVOMap.putAll(spuVOMap1);
        }
        ArrayList<ConfigOrgDetailExtend> configOrgDetailExtendArrayList = new ArrayList<>();
        for (SpuListVo spu : spuVOMap.values()) {
            ConfigOrgDetailExtend extend = new ConfigOrgDetailExtend();
            CommonGoodsDTO goodsDTO = getGoodsDTO(spu);
            extend.setDetailId(detailId);
            extend.setConfigId(configId);
            extend.setExtendType(Constants.GOODS);
            extend.setExtend(JSON.toJSONString(goodsDTO));
            extend.setKeyword(spu.getGoodsNo());
            buildConfigOrgDetailExtendCommonInfo(extend, userDTO);
            configOrgDetailExtendArrayList.add(extend);
        }
        List<List<ConfigOrgDetailExtend>> partitionGoods = Lists.partition(configOrgDetailExtendArrayList, Constants.INSERT_MAX_SIZE);
        for (List<ConfigOrgDetailExtend> configOrgDetailExtends : partitionGoods) {
            configOrgDetailExtendExtendMapper.insertConfigOrgDetailExtendList(configOrgDetailExtends);
        }
    }

    private Long inserExcludeAttribute(TokenUserDTO userDTO, Long configId, String dictCode) {
        ConfigOrgDetail configOrgDetail = new ConfigOrgDetail();
        configOrgDetail.setConfigId(configId);
        configOrgDetail.setDictCode(dictCode);
        configOrgDetail.setPerprotyType(Constants.COLLECTION);
        configOrgDetail.setPerprotyValue("");
        buildConfigOrgDetailCommonInfo(configOrgDetail, userDTO, null);
        configOrgDetailMapper.insert(configOrgDetail);
        return configOrgDetail.getId();
    }

    /**
     * 查询组货商品黑名单
     * @param ruleParam
     * @param userDTO
     * @return
     */
    @Override
    public PageResult<CommonGoodsDTO> queryGroupBlackGoods(RuleParam ruleParam, TokenUserDTO userDTO) {
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleParam.getOrgId(), Byte.valueOf(ruleParam.getConfigType()), null);
        if (Objects.isNull(configOrg)){
            return null;
        }
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Arrays.asList(ruleParam.getGoodsDictCode()));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            return null;
        }
        Long totalCount = configOrgDetailExtendExtendMapper.countConfigOrgDetailExtend(configOrg.getId(), configOrgDetails.get(0).getId());
        ruleParam.setPage((ruleParam.getPage()-1)* ruleParam.getPerPage());
        List<ConfigOrgDetailExtend> configOrgDetailExtends = configOrgDetailExtendExtendMapper.selectByDetailExtendByPage(configOrg.getId(), configOrgDetails.get(0).getId(),ruleParam.getPage(),ruleParam.getPerPage());
        if (CollectionUtils.isEmpty(configOrgDetailExtends)){
            return null;
        }
        ArrayList<CommonGoodsDTO> commonGoodsDTOList = new ArrayList<>();
        configOrgDetailExtends.stream().forEach(v->{
            CommonGoodsDTO commonGoodsDTO = JSONObject.parseObject(v.getExtend(), CommonGoodsDTO.class);
            commonGoodsDTOList.add(commonGoodsDTO);
        });
        return new PageResult<>(totalCount,commonGoodsDTOList);
    }

    @Override
    public void delGroupBlackGoods(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleAddParam.getOrgId(), Byte.valueOf(ruleAddParam.getConfigType()), null);
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Arrays.asList(ruleAddParam.getGoodsDictCode()));
        int i = configOrgDetailExtendExtendMapper.delByConfinIdAndDetailIdAndGoods(configOrg.getId(), configOrgDetails.get(0).getId(), ruleAddParam.getGoodsNoList());
    }

    @Override
    public void exportGoods(Long orgId, String dictCode, String configType, HttpServletResponse response, TokenUserDTO userDTO) throws Exception {
        try {
            String fileName = "商品黑名单" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            List<ListToExcelMultiSheetDTO> listDto = new ArrayList<>();
            ListToExcelMultiSheetDTO dto = new ListToExcelMultiSheetDTO();
            dto.setSheetName("sheet0");
            dto.setFieldMap(CommonGoodsDTO.getCommonGoodsExportMap());
            dto.setListGroup(new ArrayList());

            ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(orgId, Byte.parseByte(configType), null);
            if (Objects.isNull(configOrg)) {
                listDto.add(dto);
                HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
                return;
            }
            ConfigOrgDetailExample example = new ConfigOrgDetailExample();
            example.createCriteria().andConfigIdEqualTo(configOrg.getId()).andDictCodeEqualTo(dictCode);
            List<ConfigOrgDetail> configOrgDetails = configOrgDetailMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(configOrgDetails)) {
                listDto.add(dto);
                HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
                return;
            }
            ConfigOrgDetail configOrgDetail = configOrgDetails.get(0);
            ConfigOrgDetailExtendExample extendExample = new ConfigOrgDetailExtendExample();
            extendExample.createCriteria().andConfigIdEqualTo(configOrg.getId()).andDetailIdEqualTo(configOrgDetail.getId());
            dto.setListGroup(configOrgDetailExtendMapper.selectByExample(extendExample).stream()
                    .map(v -> JSONObject.parseObject(v.getExtend(), CommonGoodsDTO.class)).collect(Collectors.toList()));
            listDto.add(dto);
            HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
        } catch (BusinessErrorException e) {
            log.warn("导出黑名单商品异常", e);
            throw e;
        } catch (Exception e) {
            log.error("导出黑名单商品异常", e);
            throw e;
        }
    }

    @Override
    public void batchDelHotGoods(HotGoodsImportDTO hotGoodsImportDTO) {
        checkHotGoodsParam(hotGoodsImportDTO);
        OnlineHotGoodsExample goodsExample = new OnlineHotGoodsExample();
        OnlineHotGoodsExample.Criteria criteria = goodsExample.createCriteria();
        if(StringUtils.isNotBlank(hotGoodsImportDTO.getGoodsSource())){
            criteria.andGoodsSourceEqualTo(hotGoodsImportDTO.getGoodsSource());
        }
        if(StringUtils.isNotBlank(hotGoodsImportDTO.getGoodsYear())){
            criteria.andGoodsYearEqualTo(hotGoodsImportDTO.getGoodsYear());
        }
        if(StringUtils.isNotBlank(hotGoodsImportDTO.getGoodsTimeFrame())){
            criteria.andGoodsTimeFrameEqualTo(hotGoodsImportDTO.getGoodsTimeFrame());
        }
        if(StringUtils.isNotBlank(hotGoodsImportDTO.getGoodsTimeDimension())){
            criteria.andGoodsTimeDimensionEqualTo(hotGoodsImportDTO.getGoodsTimeDimension());
        }
        onlineHotGoodsMapper.deleteByExample(goodsExample);
    }

    @Override
    public void delConfigRuleInfo(Long orgId, TokenUserDTO userDTO, Integer version) {
        ConfigOrgExample configOrgExample = new ConfigOrgExample();
        configOrgExample.createCriteria().andOrgIdEqualTo(orgId).andVersionEqualTo(version).andCreatedByEqualTo(userDTO.getUserId());
        List<Long> configIdList = configOrgMapper.selectByExample(configOrgExample).stream().map(v->v.getId()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(configIdList)){
            return;
        }
        ConfigOrgDetailExample configOrgDetailExample = new ConfigOrgDetailExample();
        configOrgDetailExample.createCriteria().andConfigIdIn(configIdList);
        List<Long> detailIdList = configOrgDetailMapper.selectByExample(configOrgDetailExample).stream().map(v->v.getId()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(detailIdList)){
            return;
        }

        ConfigOrgDetailExtendExample extendExample = new ConfigOrgDetailExtendExample();
        extendExample.createCriteria().andConfigIdIn(configIdList).andDetailIdIn(detailIdList);
        configOrgDetailExtendMapper.deleteByExample(extendExample);
        configOrgDetailMapper.deleteByExample(configOrgDetailExample);
        configOrgMapper.deleteByExample(configOrgExample);
    }

    @Override
    public List<ErrorUnmanageDTO> importUnmanageGoods(MultipartFile file, Long orgId, String scopeCode, String configType, TokenUserDTO userDTO) throws Exception {
        try {
            List<CommonUnmanageDTO> commonGoodsDTOS = resolveUnmanageAndCheck(file, orgId, scopeCode, configType);
            //添加配置
            long configId = addConfig(orgId, Byte.parseByte(configType), userDTO, null);
            long detailId=0;
            List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configId,Arrays.asList(Constants.UNMANAGE_CATEGORY_LIST));
            if (CollectionUtils.isEmpty(configOrgDetails)){
                detailId = inserExcludeAttribute(userDTO, configId, Constants.UNMANAGE_CATEGORY_LIST);
            }else {
                detailId= configOrgDetails.get(0).getId().intValue();
            }

            return addUnmanageCategory(orgId, configId, detailId, commonGoodsDTOS,userDTO);
        } catch (Exception e) {
            log.warn("importUnmanageGoods|warn.", e);
            throw e;
        }
    }

    @Override
    public HotGoodsResponseDTO importHotGoods(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        try {
            HotGoodsResponseDTO hotGoodsResponseDTO = new HotGoodsResponseDTO();
//            List<HotGoodsTipsDTO> hotGoodsTipsDTOS = Lists.newArrayList();
            StringBuilder sbMessasge = new StringBuilder();
            List<HotGoodsImportDTO> hotGoodsImportDTOList = resolveHotGoodsAndCheck(file, null, sbMessasge);
            if(StringUtils.isNotBlank(sbMessasge.toString())){
                hotGoodsResponseDTO.setMessage(sbMessasge.toString());
                return hotGoodsResponseDTO;
            }
            List<OnlineHotGoods> hotGoodsList = hotGoodsImportDTOList.stream().map(v->{
                OnlineHotGoods onlineHotGoods = new OnlineHotGoods();
                BeanUtils.copyProperties(v, onlineHotGoods);
                onlineHotGoods.setCreateById(userDTO.getUserId());
                onlineHotGoods.setCreateBy(userDTO.getName());
                onlineHotGoods.setUpdateBy(userDTO.getName());
                onlineHotGoods.setUpdateById(userDTO.getUserId());
                return onlineHotGoods;
            }).collect(Collectors.toList());
            asyncTaskExecutor.execute(() -> {
                for (List<OnlineHotGoods> list : Lists.partition(hotGoodsList, 500)) {
                    onlineHotGoodsExtendMapper.batchInsert(list);
                }
            });
            hotGoodsResponseDTO.setMessage(new StringBuilder().append("导入成功").append(hotGoodsList.size()).append("条数据").toString());
            return hotGoodsResponseDTO;
        } catch (Exception e) {
            log.warn("importHotGoods|warn.", e);
            throw e;
        }

    }

    /**
     * 处理默认值
     * @param defaultValue  ruleDetailDTO
     * @return
     */
    private void buildDefaultValue(RuleDetailDTO ruleDetailDTO, String defaultValue){
        if(StringUtils.isBlank(defaultValue)){
            return;
        }

        if(DicApiEnum.isZhStoreAndGoods(ruleDetailDTO.getDictCode())){
            ruleDetailDTO.setPerprotyValueList(Arrays.asList(defaultValue.split(Constants.VALUE_SPLITE)));
            return;
        }
        // 不良库存不需要默认值
        if (StringUtils.isNotBlank(ruleDetailDTO.getDictCode()) && ruleDetailDTO.getDictCode().startsWith("blkc")) {
            return;
        }
        ruleDetailDTO.setPerprotyValue(defaultValue);
    }

    /**
     * 检查传入的门店和分类, 是否存在于不经营目录
     * @param storeIds 门店ID集合
     * @param categoryIds 分类ID集合(传入4级分类)
     * @return  Map<storeId, List<categoryId>>
     */
    @Override
    public Map<Long, List<String>> checkExistsUnmanage(List<Long> storeIds, List<String> categoryIds) {
        log.debug("检查是否存在于不经营目录, 入参门店list={}, 入参分类list={}", storeIds, categoryIds);
        Map<Long, List<String>> result = new HashMap<>();
        // 检查数据
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new BusinessErrorException("传入参数 storeCodes 不能为空!");
        }
        if (CollectionUtils.isEmpty(categoryIds)) {
            throw new BusinessErrorException("传入参数 categoryIds 不能为空!");
        }

        Optional<List<OrgInfoBaseCache>> optional = CacheVar.getStoreByStoreIdList(storeIds);
        List<OrgInfoBaseCache> storeList = new ArrayList<>();
        if (optional.isPresent()) {
            storeList = optional.get();
        }
        if (CollectionUtils.isEmpty(storeList)) {
            log.debug("检查是否存在于不经营目录, 找不到门店信息CacheVar.getStoreByStoreIdList(storeIds)! 入参门店list={}, 入参分类list={}", storeIds, categoryIds);
            return result;
        }

        // 根据门店集合查询所有的不经营品种
        storeList.forEach(orgInfoBaseCache -> {
            String storeCode = orgInfoBaseCache.getSapCode();

            // 根据CODE查询数据库
            ConfigOrgUnmanageCategoryExample example = new ConfigOrgUnmanageCategoryExample();
            ConfigOrgUnmanageCategoryExample.Criteria criteria = example.createCriteria();
            criteria.andStatusEqualTo(new Byte("0"));
            criteria.andStoreCodeEqualTo(storeCode);
            List<ConfigOrgUnmanageCategory> savedList = configOrgUnmanageCategoryMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(savedList)) {
                return;
            }
            // 遍历已保存的不经营分类
            List<String> cateList = new ArrayList<>();
            savedList.forEach(v -> {
                int cateType = v.getGoodsCategoryType();
                String cateId = String.valueOf(v.getGoodsCategoryId());
                if (StringUtils.isBlank(cateId)) {
                    return;
                }
                // 遍历传入参数 分类ID集合, 将categoryId拆分4级匹配数据库
                categoryIds.forEach(categoryId -> {
                    String goodsClass1 = "";
                    String goodsClass2 = "";
                    String goodsClass3 = "";
                    String goodsClass4 = "";
                    if (StringUtils.isBlank(categoryId)){
                        return ;
                    }
                    if (categoryId.length() == 2) {
                        goodsClass1 = categoryId;
                    } else if (categoryId.length() == 4) {
                        goodsClass1 = categoryId.substring(0,2);
                        goodsClass2 = categoryId;
                    } else if (categoryId.length() == 6) {
                        goodsClass1 = categoryId.substring(0,2);
                        goodsClass2 = categoryId.substring(0,4);
                        goodsClass3 = categoryId;
                    } else if (categoryId.length() == 8) {
                        goodsClass1 = categoryId.substring(0,2);
                        goodsClass2 = categoryId.substring(0,4);
                        goodsClass3 = categoryId.substring(0,6);
                        goodsClass4 = categoryId;
                    } else {
                        return ;
                    }
                    // 如果4级分类任意一级匹配, 那么返回
                    if ((cateType == 1 && goodsClass1.equals(cateId))
                     || (cateType == 2 && goodsClass2.equals(cateId))
                     || (cateType == 3 && goodsClass3.equals(cateId))
                     || (cateType == 4 && goodsClass4.equals(cateId))) {
                        cateList.add(categoryId);
                    }
                });
            });
            if (CollectionUtils.isNotEmpty(cateList)) {
                result.put(orgInfoBaseCache.getOutId(), cateList);
            }
        });
        log.debug("检查是否存在于不经营目录, 返回Map={}", result);
        return result;
    }

    @Override
    public BdpResponseDTO pushConfigToBdp(Long orgId, Long taskId, Integer month, Byte configType, String dataVersion) throws Exception {
        try {
            RuleDataBaseDTO baseDTO = new RuleDataBaseDTO();
            baseDTO.setDataVersion(dataVersion);
            baseDTO.setOrgId(orgId);
            baseDTO.setTaskId(taskId);
            baseDTO.setEnvironment(environment);
            baseDTO.setMonth(month);
            List<ConfigOrgDBStyleDTO> configOrgDBStyleDTOS = configOrgExtendMapper.selectConfigDBByOrgId(orgId, configType);
            if (CollectionUtils.isNotEmpty(configOrgDBStyleDTOS)) {
                List<Long> configIds = configOrgDBStyleDTOS.stream().map(ConfigOrgDBStyleDTO::getId).collect(Collectors.toList());
                List<ConfigOrgDetailDBStyleDTO> configOrgDetailDBStyleDTOS = configOrgDetailExMapper.selectConfigDetailDBByConfigIds(configIds);
                List<ConfigOrgDetailExtendDBStyleDTO> configOrgDetailExtendDBStyleDTOS = configOrgDetailExtendExtendMapper.selectConfigExtendDBByConfigIds(configIds);
                baseDTO.setConfigOrg(JacksonUtil.getObjectMapper().writeValueAsString(configOrgDBStyleDTOS));
                baseDTO.setConfigOrgDetail(JacksonUtil.getObjectMapper().writeValueAsString(configOrgDetailDBStyleDTOS));
                baseDTO.setConfigOrgDetailExtend(JacksonUtil.getObjectMapper().writeValueAsString(configOrgDetailExtendDBStyleDTOS));
            } else {
                log.info("组织:{},configType:{} 没有配置参数", orgId, configType);
                baseDTO.setConfigOrg(JacksonUtil.getObjectMapper().writeValueAsString(new ArrayList<>()));
                baseDTO.setConfigOrgDetail(JacksonUtil.getObjectMapper().writeValueAsString(new ArrayList<>()));
                baseDTO.setConfigOrgDetailExtend(JacksonUtil.getObjectMapper().writeValueAsString(new ArrayList<>()));
            }
            log.info("baseDTO:{}", JSON.toJSONString(baseDTO));
            ResponseEntity<BdpResponseDTO> responseEntity = sendService.sendDefectiveBdp(baseDTO);
            BdpResponseDTO bdpResponseDTO = responseEntity.getBody();
            log.info("adjustBdpTrackResult|bdpResponseDTO:{}.", JSONObject.toJSONString(bdpResponseDTO));
            if(ResponseCodeEnum.ERROR.getCode().equals(bdpResponseDTO.getCode())){
                throw new BusinessErrorException(ErrorCodeEnum.TASK_BDP_ERROR);
            }
            return bdpResponseDTO;
        } catch (Exception e) {
            log.error("根据orgId,configType获取配置信息失败", e);
            throw e;
        }
    }
}
